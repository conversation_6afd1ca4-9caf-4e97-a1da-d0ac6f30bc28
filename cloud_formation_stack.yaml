Parameters: 
  CodeCommitRepo:
    Type: String
    Default: my-app
    Description: "Code commit repo"
  CodeCommitBranch:
    Type: String
    Default: master
    Description: "Code commit branch"
  CodeCommitArn:
    Type: String
    Default: arn
    Description: "Code commit arn"
  EnvType:
    Description: Environment type.
    Type: String
    Default: dev
    AllowedValues: [prod, dev]
    ConstraintDescription: must specify prod, dev.
AWSTemplateFormatVersion: 2010-09-09
Conditions:
  CreateProdResources: !Equals [!Ref EnvType, prod]
Resources:
  DeployBucket:
    Type: 'AWS::S3::Bucket'
    Properties:
      WebsiteConfiguration:
        IndexDocument: index.html
  Distribution:
    Condition: CreateProdResources
    Type: "AWS::CloudFront::Distribution"
    Properties:
      DistributionConfig:
        Origins:
          - # Use the DeployBucket as the CDN origin
            DomainName: !GetAtt DeployBucket.DomainName
            Id: !Ref DeployBucket
            S3OriginConfig:
              OriginAccessIdentity: ''
        DefaultRootObject: index.html
        Enabled: true
        # Configure the caching behavior for our CDN
        DefaultCacheBehavior:
          MinTTL: 86400  # 1 day
          MaxTTL: 31536000  # 1 year
          ForwardedValues:
            QueryString: true
          TargetOriginId: !Ref DeployBucket
          ViewerProtocolPolicy: "redirect-to-https"
  CodeBuild:
    Type: 'AWS::CodeBuild::Project'
    Properties:
      Name: !Sub ${AWS::StackName}-CodeBuild
      ServiceRole: !GetAtt CodeBuildRole.Arn
      Artifacts:
        # The downloaded source code for the build will come from CodePipeline
        Type: CODEPIPELINE
        Name: MyProject
      Environment:
        # Linux container with node installed
        ComputeType: BUILD_GENERAL1_SMALL
        Type: LINUX_CONTAINER
        Image: "aws/codebuild/nodejs:8.11.0"
      Source:
        Type: CODEPIPELINE
        BuildSpec: !Sub |
          version: 0.1
          phases:
            pre_build:
              commands:
                - echo Installing source NPM dependencies...
                - npm install
            build:
              commands:
                - echo Build started on `date`
                - npm run build
            post_build:
              commands:
                # copy the contents of /build to S3
                - aws s3 cp --recursive --acl public-read ./build s3://${DeployBucket}/ 
                # set the cache-control headers for service-worker.js to prevent
                # browser caching
                - >
                  aws s3 cp --acl public-read 
                  --cache-control="max-age=0, no-cache, no-store, must-revalidate" 
                  ./build/service-worker.js s3://${DeployBucket}/
                # set the cache-control headers for index.html to prevent
                # browser caching
                - >
                  aws s3 cp --acl public-read 
                  --cache-control="max-age=0, no-cache, no-store, must-revalidate" 
                  ./build/index.html s3://${DeployBucket}/
          artifacts:
              files:
                - '**/*'
              base-directory: build
  # IAM role that allows CodeBuild to interact with S3, CloudWatch, and CloudFront
  CodeBuildRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - 
            Effect: Allow
            Principal:
              Service:
                - "codebuild.amazonaws.com"
            Action:
              - "sts:AssumeRole"
      Path: /service-role/
      Policies:
        - PolicyName: root
          PolicyDocument:
            Version: "2012-10-17"
            Statement: 
              - 
                Effect: Allow
                Action:
                  - "s3:GetObject"
                  - "s3:GetObjectVersion"
                  - "s3:GetBucketVersioning"
                  - "s3:PutObject"
                Resource: 
                  - !GetAtt PipelineBucket.Arn
                  - !Join ['', [!GetAtt PipelineBucket.Arn, "/*"]]
              - 
                Effect: Allow
                Action:
                  - "s3:GetObject"
                  - "s3:GetObjectVersion"
                  - "s3:GetBucketVersioning"
                  - "s3:PutObject"
                  - "s3:PutObjectAcl"
                Resource: 
                  - !GetAtt DeployBucket.Arn
                  - !Join ['', [!GetAtt DeployBucket.Arn, "/*"]]
              -
                Effect: Allow
                Action:
                  - "logs:CreateLogGroup"
                  - "logs:CreateLogStream"
                  - "logs:PutLogEvents"
                  - "cloudfront:CreateInvalidation"
                Resource:
                  - "*"
  CodePipeline:
    Type: 'AWS::CodePipeline::Pipeline'
    Properties:
      RoleArn: !GetAtt CodePipeLineRole.Arn
      ArtifactStore:
        Location: !Ref PipelineBucket
        Type: S3
      Stages:
        - 
          Name: Source
          Actions: 
            - 
              Name: SourceAction
              ActionTypeId: 
                Category: Source
                Owner: AWS
                Provider: CodeCommit
                Version: 1
              OutputArtifacts: 
                - 
                  Name: MyApp
              Configuration:
                RepositoryName: !Ref CodeCommitRepo
                BranchName: !Ref CodeCommitBranch
        - 
          Name: Build
          Actions: 
            - 
              Name: BuildAction
              ActionTypeId: 
                Category: Build
                Owner: AWS
                Version: 1
                Provider: CodeBuild
              InputArtifacts: 
                - 
                  Name: MyApp
              OutputArtifacts: 
                - 
                  Name: MyAppBuild
              Configuration:
                ProjectName: !Ref CodeBuild
  CodePipeLineRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - 
            Effect: Allow
            Principal:
              Service:
                - "codepipeline.amazonaws.com"
            Action:
              - "sts:AssumeRole"
      Policies:
        - PolicyName: root
          PolicyDocument:
            Version: "2012-10-17"
            Statement: 
              - 
                Effect: Allow
                Action:
                  - "s3:GetObject"
                  - "s3:GetObjectVersion"
                  - "s3:GetBucketVersioning"
                  - "s3:PutObject"
                Resource: 
                  - !GetAtt PipelineBucket.Arn
                  - !Join ['', [!GetAtt PipelineBucket.Arn, "/*"]]
              - 
                Effect: Allow
                Action:
                  - "codecommit:GetTree"
                  - "codecommit:GetBlob"
                  - "codecommit:GetReferences"
                  - "codecommit:DescribeMergeConflicts"
                  - "codecommit:GetPullRequestApprovalStates"
                  - "codecommit:BatchDescribeMergeConflicts"
                  - "codecommit:GetCommentsForComparedCommit"
                  - "codecommit:GetCommit"
                  - "codecommit:GetComment"
                  - "codecommit:GetCommitHistory"
                  - "codecommit:GetCommitsFromMergeBase"
                  - "codecommit:BatchGetCommits"
                  - "codecommit:DescribePullRequestEvents"
                  - "codecommit:GetPullRequest"
                  - "codecommit:GetPullRequestOverrideState"
                  - "codecommit:GetRepositoryTriggers"
                  - "codecommit:BatchGetRepositories"
                  - "codecommit:GitPull"
                  - "codecommit:GetCommentsForPullRequest"
                  - "codecommit:CancelUploadArchive"
                  - "codecommit:GetObjectIdentifier"
                  - "codecommit:GetFolder"
                  - "codecommit:BatchGetPullRequests"
                  - "codecommit:GetFile"
                  - "codecommit:GetUploadArchiveStatus"
                  - "codecommit:EvaluatePullRequestApprovalRules"
                  - "codecommit:GetDifferences"
                  - "codecommit:GetRepository"
                  - "codecommit:GetBranch"
                  - "codecommit:GetMergeConflicts"
                  - "codecommit:GetMergeCommit"
                  - "codecommit:GetMergeOptions"
                  - "codecommit:UploadArchive"
                Resource: 
                  - !Ref CodeCommitArn
              - 
                Effect: Allow  
                Action:
                  - "codebuild:BatchGetBuilds"
                  - "codebuild:StartBuild"
                Resource: "*"
  # Temp bucket for storing build artifacts
  PipelineBucket: 
    Type: 'AWS::S3::Bucket'
    Properties: {}