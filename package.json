{"name": "pixxa-website", "version": "0.1.0", "private": true, "dependencies": {"@babel/runtime": "^7.27.6", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.4.0", "@testing-library/user-event": "^7.2.1", "gsap": "^3.0.5", "i18next": "^19.0.3", "jquery": "^3.4.1", "lodash": "^4.17.15", "react": "^16.12.0", "react-app-polyfill": "^1.0.5", "react-cookie-consent": "^6.1.0", "react-dom": "^16.12.0", "react-helmet": "^5.2.1", "react-i18next": "^11.3.1", "react-lottie": "^1.2.3", "react-redux": "^7.1.3", "react-router-dom": "^5.1.2", "react-screen-orientation": "0.0.4", "react-scripts": "^5.0.1", "redux": "^4.0.5", "redux-devtools-extension": "^2.13.8", "three": "^0.112.1"}, "devDependencies": {"prettier": "^1.19.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}