# PIXXA Website

Showcase site for PIXXA

## Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes. See deployment for notes on how to deploy the project on a live system.

### Prerequisites

To install project you need

```
nodejs
npm
```

### Installing

To install the project you need to run

```
npm  install
```

in the root of the project and create a file .env.local with all envirorment variables

### Project configuration

Here a list of all variables

`REACT_APP_STAGE`= Ambiente di sviluppo. default: development

`REACT_APP_NAME`= Nome dell'applicazione (Verrà visualizzato come title)

`REACT_APP_LANDING_SQUARES_FRONT_QUANTITY` = Numero di quadrati grandi mostrati nel livello frontale e che seguono il movimento del mouse. default: 300

`REACT_APP_LANDING_SQUARES_BACK_QUANTITY` = Numero di quadrati piccoli mostrati nel livello posteriore e che seguono l'inverso del movimetno del mouse. default: 600

`REACT_APP_LANDING_SQUARE_FRONT_MAX_DIMENSION` = Dimensione massima dei quadrati del livello frontale. default: 12

`REACT_APP_LANDING_SQUARE_FRONT_MIN_DIMENSION` = Dimensione minima dei quadrati del livello frontale. default: 8

`REACT_APP_LANDING_SQUARE_BACK_MAX_DIMENSION` = Dimensione massima dei quadrati del livello posteriore. default: 6

`REACT_APP_LANDING_SQUARE_BACK_MIN_DIMENSION` = Dimensione minima dei quadrati del livello posteriore. default: 4

`REACT_APP_EVERY_HOW_MANY_SQUARES_A_SELF_ROTATION` = Numero che indica ogni quanti quadrati deve essercene uno che ha un'animazione custom. default: 3

`REACT_APP_LANDING_EXPLOSION_DURATION` = Durata dell'animazione di esplosione. default: 4

`REACT_APP_LANDING_EXPLOSION_DELAY` = Tempo prima dell'inizio dell'animazione di esplosione. default: 0.2

`REACT_APP_LANDING_MIN_DURATION_FAST_PIXELS` = Durata minima dell'animazione dei pixel veloci nel livello frontale e posteriore. default: 20

`REACT_APP_LANDING_MAX_DURATION_FAST_PIXELS` = Durata massima dell'animazione dei pixel veloci nel livello frontale e posteriore. default: 50

`REACT_APP_LANDING_DIMENSION_CUBE_1` = Dimensione del primo cubo. default: 25

`REACT_APP_LANDING_DIMENSION_CUBE_2` = Dimensione del secondo cubo. default: 12

`REACT_APP_LANDING_DIMENSION_CUBE_3` = Dimensione del terzo cubo. default: 18

`REACT_APP_LANDING_DIMENSION_CUBE_4` = Dimensione del quarto cubo. default: 40

`REACT_APP_LANDING_LOGO_WIDTH_WEB` = Larghezza del logo da mobile. default: 500

`REACT_APP_LANDING_LOGO_WIDTH_MOBILE` = Larghezza del logo da web. default: 300

`REACT_APP_LANDING_K_PARALLAX_CUBES` = Coefficiente di blocco per il parallax dei cubi. Più è alto meno i cubi seguiranno il mouse. (1-100). default: 70

`REACT_APP_LANDING_TRIGGER_EXPLOSION_DURATION` = Durata dell'animazione dei quadrati iniziali che vanno a creare l'esplosione. default: 3

### Coding style

In the root of project there is a file .prettierrc that contains styling rules.
You need to configure prettier for your IDE 

### Local run

Run

```
npm  start
```

in the root of the project


## Deployment

This site is deployed on AWS. We used the Cloud Formation stack in the root of the project.
The master branch is linked with AWS Pipeline. So if you commit there the code will be build and deployed in production


## Built With

* [React](https://reactjs.org/) - The web framework used
* [gsap](https://greensock.com/gsap/) - For animations

