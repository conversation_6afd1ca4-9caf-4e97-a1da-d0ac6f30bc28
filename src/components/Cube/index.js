import React, { Component } from "react";
import { TweenMax, TimelineLite, Linear } from "gsap";
import styles from "./style";
import $ from "jquery";
import { generateRandomFromRange, randomRotationDirection } from "../../utils";
import config from "../../config";

class Cube extends Component {
  constructor(props) {
    super(props);
    this.position = null;
  }
  componentDidMount() {
    const { id } = this.props;
    const { TRIGGER_DURATION, EXPLOSION_DURATION, EXPLOSION_DELAY } = config;
    let rotationTl = new TimelineLite({
      delay: 0,
      paused: true,
      repeat: -1
    });
    let randomRotationY = randomRotationDirection();
    let randomRotationX = randomRotationDirection();
    let randomRotationZ = randomRotationDirection();
    let randomRotationDur = generateRandomFromRange(60, 30, true);
    rotationTl.from(
      `#${id}`,
      randomRotationDur,
      {
        rotationY: randomRotationY,
        rotationX: randomRotationX,
        rotationZ: randomRotationZ,
        ease: Linear.easeNone
      },
      0
    );
    rotationTl.to(
      `#${id}`,
      randomRotationDur,
      {
        rotationY: randomRotationY,
        rotationX: randomRotationX,
        rotationZ: randomRotationZ,
        ease: Linear.easeNone
      },
      randomRotationDur
    );

    setTimeout(() => {
      this.position = $(`#${id}`).position();
    }, (TRIGGER_DURATION + EXPLOSION_DELAY + EXPLOSION_DURATION) * 1000);

    rotationTl.play();
  }

  handleMouseLeave = () => {
    if (this.position) {
      const { id } = this.props;
      TweenMax.to(`#${id}`, 0.5, {
        left: this.position.left + 5,
        top: this.position.top + 5
      });
    }
  };

  handleMouseEnter = () => {
    if (this.position) {
      const { id } = this.props;
      TweenMax.to(`#${id}`, 0.5, {
        left: this.position.left - 5,
        top: this.position.top - 5
      });
    }
  };

  render() {
    const {
      face,
      faceTop,
      faceBottom,
      faceRight,
      faceLeft,
      faceFront,
      faceBack
    } = styles;
    const { id, style, dimensions } = this.props;
    return (
      <div
        id={id}
        onMouseEnter={this.handleMouseEnter}
        onMouseLeave={this.handleMouseLeave}
        className="cube"
        style={{
          position: "fixed",
          ...style,
          width: dimensions,
          height: dimensions,
          transformStyle: "preserve-3d",
          transform: "rotate3d(1, 1, 1, 45deg)"
        }}
      >
        <div
          className="side1 front"
          style={Object.assign(
            {},
            face,
            {
              width: dimensions,
              height: dimensions,
              transform: `translateZ(${dimensions / 2}px)`
            },
            faceFront
          )}
        />
        <div
          className="side2 back"
          style={Object.assign(
            {},
            face,
            {
              width: dimensions,
              height: dimensions,
              transform: `rotateY(180deg) translateZ(${dimensions / 2}px)`
            },
            faceBack
          )}
        />
        <div
          className="side3 right"
          style={Object.assign(
            {},
            face,
            {
              width: dimensions,
              height: dimensions,
              transform: `rotateY(90deg) translateZ(${dimensions / 2}px)`
            },
            faceRight
          )}
        />
        <div
          className="side4 left"
          style={Object.assign(
            {},
            face,
            {
              width: dimensions,
              height: dimensions,
              transform: `rotateY(-90deg) translateZ(${dimensions / 2}px)`
            },
            faceLeft
          )}
        />
        <div
          className="side5 top"
          style={Object.assign(
            {},
            face,
            {
              width: dimensions,
              height: dimensions,
              transform: `rotateX(90deg) translateZ(${dimensions / 2}px)`
            },
            faceTop
          )}
        />
        <div
          className="side6 bottom"
          style={Object.assign(
            {},
            face,
            {
              width: dimensions,
              height: dimensions,
              transform: `rotateX(-90deg) translateZ(${dimensions / 2}px)`
            },
            faceBottom
          )}
        />
      </div>
    );
  }
}

export default Cube;
