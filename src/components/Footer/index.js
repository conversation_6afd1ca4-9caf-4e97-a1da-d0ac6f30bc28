import React, { Component } from "react";
import { compose } from "redux";
import { connect } from "react-redux";
import { withTranslation } from "react-i18next";
import styles from "./styles";
import config from "../../config";
import { TweenMax } from "gsap";
import { getFontSizeXD, isMobile } from "../../utils";

class Footer extends Component {
  componentDidMount() {
    this.showFooter();
  }

  showFooter = () => {
    const { EXPLOSION_DELAY, TRIGGER_DURATION } = config;
    TweenMax.to("#footer", 1, {
      opacity: 1,
      delay: EXPLOSION_DELAY + TRIGGER_DURATION + 4
    });
  };
  render() {
    const { footerContainerStyle, footerTextStyle } = styles;
    const { t } = this.props;
    let fontSize = isMobile() ? 8 : 12;
    return (
      <div id="footer" style={footerContainerStyle}>
        <div
          style={Object.assign(
            {},
            { fontSize: getFontSizeXD(fontSize) },
            footerTextStyle
          )}
        >
          {t("footerText")}
        </div>
      </div>
    );
  }
}

const mapStateToProps = ({ dimensions }) => {
  return {
    windowWidth: dimensions.windowWidth
  };
};

export default compose(
  withTranslation("landingPage"),
  connect(mapStateToProps)
)(Footer);
