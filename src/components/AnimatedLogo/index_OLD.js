import React, { Component } from "react";
import { connect } from "react-redux";
import { TimelineLite, Linear } from "gsap";
import styles from "./styles";
import { isMobile } from "../../utils";
import config from "./../../config";

class AnimatedLogo extends Component {
  state = {
    linesHeight: 0,
    linesWidth: 0
  };

  componentDidMount() {
    window.addEventListener("resize", this.updateLinesDimensions);
    this.updateLinesDimensions();

    const { TRIGGER_DURATION, EXPLOSION_DELAY } = config;
    let dur = 2;
    let linesDur = 1;
    let startY = -1000;
    let startX = -4000;
    let linesTl = new TimelineLite({
      delay: EXPLOSION_DELAY + TRIGGER_DURATION,
      paused: true
    });
    let tl = new TimelineLite({
      delay: EXPLOSION_DELAY + TRIGGER_DURATION + 0.5,
      paused: true
    });

    linesTl.from(".lineV", linesDur, { y: startY }, 0);
    linesTl.from(".lineH", linesDur, { x: startX }, 0);
    linesTl.to(".lineV", linesDur * 2, { y: 1000, ease: Linear.easeNone }, 1);
    linesTl.to(".lineH", linesDur * 2, { x: 4000, ease: Linear.easeNone }, 1);

    tl.from(".slice", dur, { autoAlpha: 0 }, 0)
      .from("g .square", dur, { autoAlpha: 0 }, 0.5)
      .from("g .square2", dur, { autoAlpha: 0 }, 0.8)
      .from(".letter_p", dur, { autoAlpha: 0 }, 0.2)
      .from(".letter_i", dur, { autoAlpha: 0 }, 0.3)
      .from(".letter_x", dur, { autoAlpha: 0 }, 0.4)
      .from(".letter_x2", dur, { autoAlpha: 0 }, 0.5)
      .from(".letter_a", dur, { autoAlpha: 0 }, 0.6)
      .from(".sub_s", dur, { autoAlpha: 0 }, 0.7)
      .from(".sub_l", dur, { autoAlpha: 0 }, 0.8)
      .from(".sub_i", dur, { autoAlpha: 0 }, 0.9)
      .from(".sub_c", dur, { autoAlpha: 0 }, 1)
      .from(".sub_e", dur, { autoAlpha: 0 }, 1.1)
      .from(".sub_s2", dur, { autoAlpha: 0 }, 1.2)
      .from(".sub_o", dur, { autoAlpha: 0 }, 1.3)
      .from(".sub_f", dur, { autoAlpha: 0 }, 1.4)
      .from(".sub_d", dur, { autoAlpha: 0 }, 1.5)
      .from(".sub_i2", dur, { autoAlpha: 0 }, 1.6)
      .from(".sub_g", dur, { autoAlpha: 0 }, 1.7)
      .from(".sub_i3", dur, { autoAlpha: 0 }, 1.8)
      .from(".sub_t", dur, { autoAlpha: 0 }, 1.9)
      .from(".sub_a", dur, { autoAlpha: 0 }, 2.0)
      .from(".sub_l2", dur, { autoAlpha: 0 }, 2.1);

    linesTl.play();
    tl.play();
  }

  updateLinesDimensions = () => {
    let linesHeight = document.getElementById("animatedLinesSVG").clientHeight;
    let linesWidth = document.getElementById("animatedLinesSVG").clientWidth;

    this.setState({
      linesHeight,
      linesWidth
    });
  };

  render() {
    const { st0, st1, st4, st7, logo, lines } = styles;
    const { windowWidth, windowHeight } = this.props;
    const { LANDING_LOGO_WIDTH_WEB, LANDING_LOGO_WIDTH_MOBILE } = config;
    let lineHeight = 280;
    let lineWidth = 700;
    let width = isMobile() ? LANDING_LOGO_WIDTH_MOBILE : LANDING_LOGO_WIDTH_WEB;
    return (
      <div
        style={{
          width: windowWidth,
          height: windowHeight,
          display: "flex",
          justifyContent: "center",
          alignItems: "center"
        }}
      >
        <div style={Object.assign({}, lines, { width: width + 100 })}>
          <svg id="animatedLinesSVG" x={0} y={0} viewBox="0 0 716.6 319.17">
            <g>
              <rect
                style={st7}
                className="lineV"
                x="177.73"
                y="13.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="197.84"
                y="13.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="72.04"
                y="13.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="102.73"
                y="23.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x1="135.64"
                y1="3.31"
                x2="135.64"
                y2="282.45"
              />
              <rect
                className="lineV"
                style={st7}
                x="121.55"
                y="13.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="706.6"
                y="112.89"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="696.6"
                y="132.98"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="284.46"
                y="23.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="304.57"
                y="3.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="323.48"
                y="13.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="343.68"
                y="13.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="362.59"
                y="40.03"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="443.93"
                y="0"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="462.84"
                y="13.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="504.94"
                y="3.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="216.17"
                y="13.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="423.81"
                y="23.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="522.94"
                y="13.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="543.64"
                y="23.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="562.13"
                y="13.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="611.9"
                y="3.31"
                width="1"
                height={lineHeight}
              />
              <rect
                className="lineV"
                style={st7}
                x="630.43"
                y="23.31"
                width="1"
                height={lineHeight}
              />

              <rect
                className="lineH"
                style={st7}
                x="10.01"
                y="100.09"
                width={lineWidth}
                height={1}
              />
              <rect
                className="lineH"
                style={st7}
                x="7"
                y="93.06"
                width={lineWidth}
                height={1}
              />
              <rect
                className="lineH"
                style={st7}
                x="20.05"
                y="186.13"
                width={lineWidth}
                height={1}
              />
              <rect
                className="lineH"
                style={st7}
                x="10.5"
                y="179.1"
                width={lineWidth}
                height={1}
              />
              <rect
                className="lineH"
                style={st7}
                x="0.5"
                y="198.8"
                width={lineWidth}
                height={1}
              />
              <rect
                className="lineH"
                style={st7}
                x="10.5"
                y="230.22"
                width={lineWidth}
                height={1}
              />
            </g>
          </svg>
        </div>
        <div style={Object.assign({}, logo, { width: width })}>
          <svg id="animatedLogoSVG" x={0} y={0} viewBox="0 0 559.9 130.16">
            <path
              className="sub_s"
              style={st0}
              d="M135.39,117.86c-1.69-0.57-3.06-1.37-4.11-2.42c-1.05-1.05-1.57-2.35-1.57-3.9c0-1.52,0.54-2.83,1.63-3.92
	c1.09-1.09,2.41-1.64,3.96-1.64c1.53,0,2.84,0.54,3.94,1.63c0.62,0.6,1.13,1.55,1.55,2.85l-2.8,1.1c-0.34-0.92-0.65-1.51-0.93-1.79
	c-0.49-0.48-1.07-0.72-1.76-0.72c-0.7,0-1.29,0.24-1.78,0.72s-0.73,1.07-0.73,1.77s0.24,1.29,0.73,1.78
	c0.55,0.54,1.3,1.03,2.27,1.48c1.87,0.61,3.46,1.56,4.76,2.87c1.46,1.44,2.19,3.19,2.19,5.25s-0.73,3.82-2.19,5.27
	s-3.21,2.19-5.26,2.19c-2.06,0-3.81-0.73-5.27-2.19c-1.16-1.16-1.9-2.77-2.21-4.82l3-1.09c0.19,1.76,0.65,3.01,1.39,3.75
	c0.86,0.86,1.88,1.28,3.09,1.28c1.2,0,2.23-0.43,3.09-1.28s1.28-1.89,1.28-3.1c0-1.2-0.43-2.23-1.28-3.09
	C137.57,119,136.57,118.35,135.39,117.86z"
            />
            <path
              className="sub_l"
              style={st0}
              d="M155.83,130.11v-23.87h3.09V127h8.53v3.1H155.83z"
            />
            <path
              style={st0}
              className="sub_i"
              d="M184.5,106.23v23.87h-3.09v-23.87H184.5z"
            />
            <path
              className="sub_c"
              style={st0}
              d="M217.84,124.42l2.31,2.07c-2.83,2.58-5.81,3.86-8.97,3.86c-3.37,0-6.25-1.19-8.62-3.57
	c-2.38-2.38-3.57-5.25-3.57-8.62c0-3.36,1.19-6.24,3.57-8.62s5.25-3.58,8.62-3.58c3.15,0,6.14,1.29,8.97,3.86l-2.31,2.1
	c-2.13-1.89-4.35-2.83-6.65-2.83c-2.5,0-4.63,0.88-6.41,2.66s-2.66,3.91-2.66,6.42c0,2.52,0.89,4.66,2.66,6.43s3.91,2.66,6.41,2.66
	C213.49,127.26,215.71,126.31,217.84,124.42z"
            />
            <path
              className="sub_e"
              style={st0}
              d="M246.81,106.23v3.09h-9.77v7.29h9.77v3.09h-9.77V127h9.77v3.1h-12.86v-23.87H246.81z"
            />
            <path
              className="sub_s2"
              style={st0}
              d="M267.07,117.86c-1.69-0.57-3.06-1.37-4.11-2.42c-1.05-1.05-1.57-2.35-1.57-3.9c0-1.52,0.54-2.83,1.63-3.92
	c1.09-1.09,2.41-1.64,3.96-1.64c1.53,0,2.84,0.54,3.94,1.63c0.62,0.6,1.13,1.55,1.55,2.85l-2.8,1.1c-0.34-0.92-0.65-1.51-0.93-1.79
	c-0.49-0.48-1.07-0.72-1.76-0.72c-0.7,0-1.29,0.24-1.78,0.72s-0.73,1.07-0.73,1.77s0.24,1.29,0.73,1.78
	c0.55,0.54,1.3,1.03,2.27,1.48c1.87,0.61,3.46,1.56,4.76,2.87c1.46,1.44,2.19,3.19,2.19,5.25s-0.73,3.82-2.19,5.27
	s-3.21,2.19-5.26,2.19c-2.06,0-3.81-0.73-5.27-2.19c-1.16-1.16-1.9-2.77-2.21-4.82l3-1.09c0.19,1.76,0.65,3.01,1.39,3.75
	c0.86,0.86,1.88,1.28,3.09,1.28c1.2,0,2.23-0.43,3.09-1.28s1.28-1.89,1.28-3.1c0-1.2-0.43-2.23-1.28-3.09
	C269.25,119,268.25,118.35,267.07,117.86z"
            />
            <path
              className="sub_o"
              style={st0}
              d="M328.63,109.5c2.39,2.39,3.58,5.27,3.58,8.64c0,3.37-1.19,6.25-3.58,8.64c-2.39,2.39-5.27,3.58-8.64,3.58
	c-3.37,0-6.25-1.19-8.64-3.58c-2.39-2.39-3.58-5.27-3.58-8.64c0-3.37,1.19-6.25,3.58-8.64c2.39-2.39,5.27-3.58,8.64-3.58
	C323.37,105.92,326.25,107.12,328.63,109.5z M320,109.04c-2.52,0-4.66,0.89-6.44,2.66s-2.66,3.92-2.66,6.44s0.89,4.66,2.67,6.44
	c1.78,1.78,3.92,2.67,6.43,2.67c2.52,0,4.66-0.89,6.44-2.67c1.78-1.78,2.66-3.93,2.66-6.44s-0.89-4.66-2.66-6.44
	S322.51,109.04,320,109.04z"
            />
            <path
              className="sub_f"
              style={st0}
              d="M357.9,106.23v3.09h-8.4v7.29h8.4v3.09h-8.4v10.4h-3.09v-23.87H357.9z"
            />
            <g>
              <path
                className="sub_d"
                style={st4}
                d="M395,105.73h-2.77v24.87h0.5H395c4-0.02,7.17-1.18,9.42-3.44c2.46-2.47,3.71-5.5,3.71-8.99
		c0-3.48-1.25-6.51-3.71-8.99C402.17,106.92,399,105.76,395,105.73z M404.02,118.17c0,2.37-0.84,4.41-2.51,6.08
		c-1.36,1.37-3.1,2.11-5.19,2.21v-16.57c2.08,0.1,3.83,0.84,5.19,2.2C403.18,113.77,404.02,115.81,404.02,118.17z"
              />
              <rect
                className="sub_i2"
                x="421.26"
                y="105.73"
                style={st4}
                width="4.09"
                height="24.87"
              />
              <path
                className="sub_g"
                style={st4}
                d="M451.54,109.6c2.17,0,4.3,0.91,6.32,2.71l0.34,0.3l3.05-2.78l-0.41-0.37c-2.91-2.65-6.04-4-9.3-4
		c-3.49,0-6.51,1.25-8.98,3.73c-2.46,2.47-3.71,5.49-3.71,8.98c0,3.49,1.25,6.51,3.71,8.98c2.46,2.46,5.48,3.71,8.98,3.71
		c3.28,0,6.23-1.23,8.77-3.66l0.15-0.15v-9.2h-4.09v7.46c-1.29,0.96-2.91,1.45-4.84,1.45c-2.35,0-4.38-0.84-6.05-2.51
		c-1.67-1.67-2.52-3.71-2.52-6.08c0-2.36,0.85-4.4,2.52-6.06C447.16,110.44,449.19,109.6,451.54,109.6z"
              />
              <rect
                className="sub_i3"
                x="474.49"
                y="105.73"
                style={st4}
                width="4.09"
                height="24.87"
              />
              <polygon
                className="sub_t"
                style={st4}
                points="490.96,109.82 496.52,109.82 496.52,130.61 500.63,130.61 500.63,109.82 506.19,109.82 506.19,105.73
		490.96,105.73 	"
              />
              <path
                className="sub_a"
                style={st4}
                d="M524.51,105.73l-9.05,24.87h4.36l3.06-8.44h5.62l3.07,8.44h4.35l-9.05-24.87H524.51z M527.03,118.07h-2.65
		l1.32-3.63L527.03,118.07z"
              />
              <polygon
                className="sub_l2"
                style={st4}
                points="551.87,126.5 551.87,105.73 547.78,105.73 547.78,130.61 560.4,130.61 560.4,126.5 	"
              />
            </g>
            <path
              style={st0}
              className="slice"
              d="M100.76-0.05c-8.28,0.11-16.56,0.36-24.85,0.53c-8.28,0.16-16.56,0.47-24.85,0.73
	C42.78,1.45,34.5,1.85,26.22,2.17C17.94,2.57,9.65,2.97,1.37,3.51c8.28,0.55,16.56,0.94,24.85,1.34c8.28,0.32,16.56,0.73,24.85,0.96
	c8.28,0.26,16.56,0.57,24.85,0.73c6.48,0.14,12.96,0.32,19.44,0.44L84.67,31.28L78.36,45.9l-6.25,14.65
	c-4.13,9.78-8.27,19.55-12.33,29.37c-0.24,0.58-0.48,1.16-0.72,1.74l-8-5.62l-8.64,6.28l-3.93-8.78l-5.45-11.9
	c-3.6-7.94-7.32-15.84-11.03-23.74c-3.74-7.89-9.36-20.24-13.26-28.06c2.84,8.26,7.67,20.95,10.68,29.15
	c3.04,8.18,6.06,16.37,9.2,24.51l4.66,12.23l4.77,12.18c3.18,8.12,6.35,16.25,9.64,24.32l3.23,7.94l3.48-7.93
	c4.26-9.72,8.6-19.42,12.78-29.17c4.23-9.74,8.37-19.51,12.52-29.28l6.18-14.68L92,34.39L104.03,4.9l2.05-5.01L100.76-0.05z"
            />
            <g>
              <rect
                className="square"
                x="31.97"
                y="20.77"
                style={st1}
                width="19.11"
                height="19.11"
              />
              <rect
                className="square2"
                x="50.78"
                y="48.14"
                style={st1}
                width="14.1"
                height="14.1"
              />
            </g>
            <path
              className="letter_a"
              style={st0}
              d="M473.03,43.18c0-6.03,1.14-11.67,3.41-16.91c2.27-5.24,5.37-9.79,9.29-13.65c3.91-3.86,8.49-6.93,13.73-9.21
	C504.7,1.14,510.34,0,516.37,0c6.03,0,11.67,1.14,16.91,3.41c5.24,2.28,9.79,5.35,13.65,9.21c3.86,3.86,6.93,8.41,9.21,13.65
	c2.27,5.24,3.41,10.87,3.41,16.91v49.85h-18.42V78.58c-7.2,5.18-15.45,7.78-24.77,7.78c-6.56,0-12.52-1.11-17.86-3.33
	c-5.34-2.22-9.9-5.29-13.65-9.21c-3.76-3.91-6.67-8.49-8.73-13.73S473.03,49.21,473.03,43.18z M491.45,43.18
	c0,3.39,0.66,6.59,1.99,9.6c1.33,3.02,3.11,5.64,5.34,7.86s4.86,4,7.88,5.32c3.03,1.32,6.24,1.98,9.64,1.98s6.61-0.61,9.63-1.83
	c3.03-1.22,5.65-2.94,7.88-5.16c2.23-2.22,4.01-4.84,5.33-7.86c1.33-3.02,1.99-6.32,1.99-9.92c0-3.39-0.67-6.59-1.99-9.6
	c-1.33-3.02-3.11-5.64-5.33-7.86c-2.23-2.22-4.86-3.99-7.88-5.32c-3.03-1.32-6.24-1.98-9.63-1.98s-6.61,0.66-9.64,1.98
	c-3.03,1.32-5.65,3.1-7.88,5.32s-4.01,4.84-5.34,7.86C492.11,36.59,491.45,39.79,491.45,43.18z"
            />
            <path
              className="letter_p"
              style={st0}
              d="M210.41,60.09c-2.06,5.24-4.97,9.82-8.73,13.73c-3.76,3.92-8.31,6.98-13.65,9.21
	c-5.34,2.22-11.3,3.33-17.86,3.33c-9.31,0-17.57-2.59-24.77-7.78v14.44h-18.42V43.18c0-6.03,1.14-11.67,3.41-16.91
	c2.28-5.24,5.35-9.79,9.21-13.65c3.86-3.86,8.41-6.93,13.65-9.21C158.5,1.14,164.14,0,170.17,0c6.03,0,11.67,1.14,16.91,3.41
	c5.24,2.28,9.82,5.35,13.73,9.21c3.92,3.86,7.01,8.41,9.29,13.65c2.28,5.24,3.41,10.87,3.41,16.91
	C213.51,49.21,212.48,54.85,210.41,60.09z M193.1,33.58c-1.33-3.02-3.11-5.64-5.34-7.86s-4.86-3.99-7.88-5.32
	c-3.03-1.32-6.24-1.98-9.64-1.98s-6.61,0.66-9.63,1.98c-3.03,1.32-5.65,3.1-7.88,5.32c-2.23,2.22-4.01,4.84-5.33,7.86
	c-1.33,3.02-1.99,6.22-1.99,9.6c0,3.6,0.67,6.91,1.99,9.92c1.33,3.02,3.11,5.64,5.33,7.86c2.23,2.22,4.86,3.94,7.88,5.16
	c3.03,1.22,6.24,1.83,9.63,1.83s6.61-0.66,9.64-1.98c3.03-1.32,5.65-3.1,7.88-5.32s4.01-4.84,5.34-7.86
	c1.33-3.02,1.99-6.22,1.99-9.6C195.09,39.79,194.43,36.59,193.1,33.58z"
            />
            <path
              className="letter_i"
              style={st0}
              d="M233.64,6.83h18.73V86.2h-18.73V6.83z"
            />
            <polygon
              className="letter_x"
              style={st4}
              points="352.69,6.83 334.15,6.83 312.68,34.41 291.38,6.83 272.36,6.83 303.26,46.51 272.36,86.2 291.38,86.13
	312.66,58.6 334.09,86.13 352.69,86.2 322.02,46.5 "
            />
            <polygon
              className="letter_x2"
              style={st0}
              points="452.98,6.83 434.44,6.83 412.97,34.41 391.67,6.83 372.66,6.83 403.55,46.51 372.66,86.2 391.67,86.13
	412.96,58.6 434.39,86.13 452.98,86.2 422.31,46.5 "
            />
          </svg>
        </div>
      </div>
    );
  }
}

const mapStateToProps = ({ dimensions }) => {
  return {
    windowWidth: dimensions.windowWidth,
    windowHeight: dimensions.windowHeight
  };
};

export default connect(mapStateToProps)(AnimatedLogo);
