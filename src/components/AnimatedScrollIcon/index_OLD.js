import React, { Component, Fragment } from "react";
import { TimelineLite, Power2 } from "gsap";
import { withTranslation } from "react-i18next";
import $ from "jquery";
import styles from "./styles";

class AnimatedScrollIcon extends Component {
  componentDidMount() {
    let tl = new TimelineLite({ repeat: -1 });
    let dur = 0.8;
    let easeOut = Power2.easeOut;
    let easeIn = Power2.easeIn;
    tl.to(
      "#animatedScrollIconContainer g rect",
      dur,
      {
        attr: { height: 0 },
        ease: easeOut
      },
      0
    );
    tl.to(
      "#animatedScrollIconContainer",
      dur,
      { bottom: 10, ease: easeOut },
      0
    );
    tl.to(
      "#animatedScrollIconContainer g path",
      dur,
      {
        attr: { d: "M 0 0 V 0" },
        ease: easeOut,
        onComplete: () => this.setUpElements(dur)
      },
      0
    );
    tl.to(
      "#animatedScrollIconContainer g rect",
      dur,
      {
        attr: { height: 60 },
        ease: easeIn,
        delay: dur
      },
      dur
    );
    tl.to(
      "#animatedScrollIconContainer g path",
      dur,
      { attr: { d: "M 0 0 V 50" }, ease: easeIn, delay: dur },
      dur
    );
    tl.play();
  }

  setUpElements = delay => {
    let elements = $(
      "#animatedScrollIconContainer g rect, #animatedScrollIconContainer g path, #animatedScrollIconContainer"
    );
    elements.hide();
    setTimeout(() => {
      $("#animatedScrollIconContainer").css("bottom", 60);
      elements.show();
    }, delay * 1000);
  };

  render() {
    const { t } = this.props;
    const { a, b, root, scrollDownTextStyle } = styles;
    return (
      <Fragment>
        <div id="animatedScrollIconContainer" style={root}>
          <svg width="36" height="81.5" viewBox="0 0 36 81.5">
            <g transform="translate(-717 -810.5)">
              <rect
                style={a}
                width="36"
                height="66"
                rx="18"
                transform="translate(717 826)"
              />
              <path style={b} d="M 0 0 V 50" transform="translate(735 820)" />
            </g>
          </svg>
        </div>
        <div id="animatedScrollIconText" style={scrollDownTextStyle}>
          {t("scrollDown")}
        </div>
      </Fragment>
    );
  }
}

export default withTranslation("landingPage")(AnimatedScrollIcon);
