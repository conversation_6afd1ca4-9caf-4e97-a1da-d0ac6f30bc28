{"id": "fullStackDeveloper", "title": "Full Stack Developer", "subTitle": "Junior - Middle - Senior", "heading": "Requirements", "content": "We want to include a Full Stack Developer in the team to whom we will entrust the management of the app and management platforms, understood as development, maintenance and integration with other business processes. The resource will work closely with the CEO and in a team with UX Designers and other Devs.", "contenuto1": "Knowledge/Experience in Web Programming (PHP, HTML5, CSS3 and Javascript)", "contenuto2": "Knowledge/Experience in React JS and React Native frameworks", "contenuto3": "Knowledge/Experience in Laravel framework", "contenuto4": "Database (MySQL, MariaDB, MongoDB…) and data modeling", "contenuto5": "Development of REST API", "contenuto6": "DevOps approach"}