{"skill": "Skill", "expertise": "& Expertise", "digital": "Il digital", "madeInSud": "MADE IN SUD", "payload": "\"La forza non è solo nelle idee: è questione d'impasto.\"", "payloadSmall": "Dall’incontro del pixel, atomo del mondo digitale, con la pizza, simbolo di un intero territorio, nasce Pixxa, ", "payloadSmall2": "la nuova Digital House.", "pencil": "MATITA", "pizza": "PIZZA", "pixel": "PIXEL", "officeEnvironmentTitle": "Office Environment", "officeEnvironmentDescr": "L’office environment gioca un ruolo chiave nella motivazione e nella salute dei dipendenti. Prestare attenzione all’ambiente e arredarlo con stile, permette di sfruttare il potenziale di chi lo occupa.", "officeEnvironmentEvidence": "Aiutiamo a creare un ambiente che fa la differenza.", "officeEnvironmentSlogan1": "\"Sforniamo ", "officeEnvironmentSloganEvidence": "creatività\"", "officeEnvironmentSlogan2": "", "webAppDevTitle": "Web & APP Development", "webAppDevDescr": "Grazie alla creatività e alla forza della programmazione curiamo progetti digitali, a partire dall’ ideazione fino alla realizzazione.", "webAppDevEvidence": "Dal Mobile first al Web base, con un appoccio Agile.", "webAppDevSlogan1": "\"<PERSON><PERSON>, da quella che prendi sempre alla ", "webAppDevSloganEvidence": "gourmet", "webAppDevSlogan2": "\"", "socialMediaMarketingTitle": "Social Media Marketing", "socialMediaMarketingDescr": "Curiamo tutti gli aspetti della comunicazione del brand su web e sui social attraverso campagne mirate.", "socialMediaMarketingEvidence": "Dai contenuti all’analytics, miglioriamo la brand reputation.", "socialMediaMarketingSlogan1": "\"O<PERSON>uno ha i suoi ", "socialMediaMarketingSloganEvidence": "gusti", "socialMediaMarketingSlogan2": ", ma l'ananas no!\"", "userExperienceTitle": "User Experience", "userExperienceDescr": "Studiamo come l’utente interagisce con il prodotto e percepisce il brand.", "userExperienceEvidence": "Disegniamo interazioni focalizzate sull’usabitità.", "userExperienceSlogan1": "\"Prepariamo l'", "userExperienceSloganEvidence": "impasto", "userExperienceSlogan2": "\"", "graphicAndVideoTitle": "Graphic & Video", "graphicAndVideoDescr": "Definiamo lo stile dell’azienda dall’immagine coordinata al manifesto.", "graphicAndVideoEvidence": "Realizziamo progetti di comunicazione sia per la stampa che per il web.", "graphicAndVideoSlogan1": "\"Gli ", "graphicAndVideoSloganEvidence": "<PERSON>i ", "graphicAndVideoSlogan2": "fanno la differenza\"", "list": {"officeEnvironment": {"personalizedWorkstation": "PERSONALIZED WORKSTATION", "comfortableOpenSpaces": "COMFORTABLE OPEN SPACES"}, "webAppDev": {"app": "APP NATIVE & HYBRID", "web": "WEB FULL-STACK", "sap": "SAP MOBILE"}, "socialMediaMarketing": {"storytelling": "STORYTELLING", "edPlan": "PIANO EDITORIALE", "campaign": "CAMPAGNE ADV", "instantMarketing": "INSTANT MARKETING", "abTest": "A/B TEST", "monitoring": "MONITORING"}, "userExperience": {"competitors": "ANALISI DEI COMPETITOR", "personas": "USER PERSONAS", "architecture": "INFORMATION ARCHITECTURE", "wireframe": "WIREFRAME", "prototype": "PROTOTIPAZIONE", "testing": "USER TESTING", "interface": "USER INTERFACE"}, "graphicAndVideo": {"logo": "MARCHIO E LOGOTIPO", "identity": "BRAND IDENTITY", "design": "GRAPHIC DESIGN", "packaging": "PACKAGING", "photo": "FOTOGRAFIA", "videoMaking": "VIDEO MAKING", "videoEditing": "VIDEO EDITING"}}}