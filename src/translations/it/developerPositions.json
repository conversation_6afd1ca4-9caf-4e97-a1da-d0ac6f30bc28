{"id": "fullStackDeveloper", "title": "Full Stack Developer", "subTitle": "Junior - Middle - Senior", "heading": "Requisiti", "content": "Vogliamo inserire nel team una figura di Full Stack Developer al quale affidare la gestione dell'app e delle piattaforme gestionali, intesa come sviluppo, manutenzione e integrazione con gli altri processi aziendali. La risorsa lavorerà a stretto contatto con la CEO e in team con gli UX Designers e gli altri Dev.", "contenuto1": "Conoscenza/Esperienza Programmazione Web (PHP, HTML5, CSS3 e Javascript)", "contenuto2": "Conoscenza/Esperienza framework React JS e React Native", "contenuto3": "Conoscenza/Esperienza framework Laravel", "contenuto4": "Database (MySQL, MariaDB, MongoDB…) e modellazione dati", "contenuto5": "Sviluppo di REST API", "contenuto6": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}